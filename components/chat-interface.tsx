"use client";

import React, { useState, useRef, useEffect } from "react";
import { IconSend, Icon<PERSON><PERSON>clip, IconRobot, IconUser } from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { FileUpload } from "@/components/file-upload";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { UploadedFile, useFileManager } from "@/lib/file-manager";
import { geminiService, ChatMessage } from "@/lib/gemini-service";

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  attachedFiles?: string[];
  isError?: boolean;
  errorType?: string;
}

export function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hello! I\'m your AI assistant powered by <PERSON>. I can help you analyze data, answer questions, and work with your uploaded JSON files. How can I assist you today?',
      sender: 'ai',
      timestamp: new Date(),
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [geminiStatus, setGeminiStatus] = useState<{ ready: boolean; error?: string }>({ ready: false });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { loadFiles } = useFileManager();

  useEffect(() => {
    const files = loadFiles();
    setUploadedFiles(files);

    // Check Gemini service status
    const status = geminiService.getStatus();
    setGeminiStatus(status);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date(),
      attachedFiles: uploadedFiles.length > 0 ? uploadedFiles.map(f => f.name) : undefined,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Check if Gemini service is ready
      if (!geminiStatus.ready) {
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `Service Error: ${geminiStatus.error || 'Gemini AI service is not available'}. Please check your configuration.`,
          sender: 'ai',
          timestamp: new Date(),
          isError: true,
          errorType: 'SERVICE_ERROR',
        };
        setMessages(prev => [...prev, errorMessage]);
        setIsLoading(false);
        return;
      }

      // Add user message to chat history
      const newChatHistory: ChatMessage[] = [
        ...chatHistory,
        { role: 'user', parts: [{ text: userMessage.content }] }
      ];

      // Send message to Gemini
      const response = await geminiService.sendMessage(
        userMessage.content,
        uploadedFiles.length > 0 ? uploadedFiles : undefined,
        chatHistory
      );

      if (response.success && response.message) {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: response.message,
          sender: 'ai',
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, aiResponse]);

        // Update chat history
        setChatHistory([
          ...newChatHistory,
          { role: 'model', parts: [{ text: response.message }] }
        ]);
      } else {
        // Handle API errors
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `Error: ${response.error || 'Failed to get response from AI'}`,
          sender: 'ai',
          timestamp: new Date(),
          isError: true,
          errorType: response.errorType,
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'An unexpected error occurred. Please try again.',
        sender: 'ai',
        timestamp: new Date(),
        isError: true,
        errorType: 'UNKNOWN',
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(files);
  };

  return (
    <div className="flex flex-1 flex-col h-[calc(100vh-var(--header-height))]">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6 h-full">
          <div className="px-4 lg:px-6 flex flex-col h-full">
            <Card className="flex flex-col h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconRobot className="h-5 w-5" />
                  AI Chat Assistant
                  <div className="ml-auto flex items-center gap-2">
                    <Badge
                      variant={geminiStatus.ready ? "secondary" : "destructive"}
                      className="text-xs"
                    >
                      {geminiStatus.ready ? "✓ Gemini Ready" : "⚠ Service Error"}
                    </Badge>
                  </div>
                </CardTitle>
                {!geminiStatus.ready && geminiStatus.error && (
                  <Alert className="mt-2">
                    <AlertDescription className="text-sm">
                      <strong>Service Issue:</strong> {geminiStatus.error}
                    </AlertDescription>
                  </Alert>
                )}
              </CardHeader>
              <CardContent className="flex flex-col h-full p-0">
                {/* Messages Area */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg p-3 ${
                          message.sender === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : message.isError
                            ? 'bg-destructive/10 border border-destructive/20'
                            : 'bg-muted'
                        }`}
                      >
                        <div className="flex items-start gap-2">
                          {message.sender === 'ai' && (
                            <IconRobot className="h-4 w-4 mt-0.5 flex-shrink-0" />
                          )}
                          {message.sender === 'user' && (
                            <IconUser className="h-4 w-4 mt-0.5 flex-shrink-0" />
                          )}
                          <div className="flex-1">
                            <p className={`text-sm whitespace-pre-wrap ${
                              message.isError ? 'text-destructive' : ''
                            }`}>
                              {message.content}
                            </p>
                            {message.attachedFiles && (
                              <div className="mt-2 flex flex-wrap gap-1">
                                {message.attachedFiles.map((fileName, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {fileName}
                                  </Badge>
                                ))}
                              </div>
                            )}
                            <p className="text-xs opacity-70 mt-1">
                              {message.timestamp.toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-muted rounded-lg p-3">
                        <div className="flex items-center gap-2">
                          <IconRobot className="h-4 w-4" />
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* File Upload Area */}
                {showFileUpload && (
                  <div className="border-t p-4">
                    <FileUpload
                      onFileUpload={handleFileUpload}
                      maxFiles={3}
                      className="max-w-md"
                    />
                  </div>
                )}

                {/* Input Area */}
                <div className="border-t p-4">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowFileUpload(!showFileUpload)}
                      className="flex-shrink-0"
                    >
                      <IconPaperclip className="h-4 w-4" />
                    </Button>
                    <Input
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message here..."
                      className="flex-1"
                      disabled={isLoading}
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputMessage.trim() || isLoading}
                      className="flex-shrink-0"
                    >
                      <IconSend className="h-4 w-4" />
                    </Button>
                  </div>
                  {uploadedFiles.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      <span className="text-xs text-muted-foreground">Attached files:</span>
                      {uploadedFiles.map((file) => (
                        <Badge key={file.id} variant="secondary" className="text-xs">
                          {file.name}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
