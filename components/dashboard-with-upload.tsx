"use client";

import React, { useState } from "react";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { DataTable } from "@/components/data-table";
import { FileUpload } from "@/components/file-upload";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IconUpload, IconDatabase } from "@tabler/icons-react";
import { Badge } from "@/components/ui/badge";

import defaultData from "../app/dashboard/finance-data.json";

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  content: any;
  uploadedAt: Date;
}

interface ProcessedInstrument {
  id: number;
  instrument: string;
  assetClass: string;
  calledAmount: number;
  commitmentAmount: number;
  status: "Paired" | "Unpaired";
  missingType?: "Called" | "Commitment";
}

interface RawDataEntry {
  // Support both formats - your actual data format and the expected format
  instrument?: string;
  Instrument?: string;
  assetClass?: string;
  "Asset Class"?: string;
  outstanding?: number;
  "Value WA Portfolio CCY"?: string | number;
  "Value Portfolio CCY"?: string | number;
  "Capital Commitment"?: string;
  "Paid Commitment"?: string;
  "Outstanding Commitment"?: string;
  [key: string]: any;
}

// Function to process uploaded data and find paired/unpaired instruments
function processInstrumentData(rawData: RawDataEntry[]): {
  pairedInstruments: ProcessedInstrument[];
  unpairedInstruments: ProcessedInstrument[];
} {
  const processedInstruments: ProcessedInstrument[] = [];
  let id = 1;

  // Check if data has the expected "- Called" / "- Commitment" format
  const hasCallCommitmentFormat = rawData.some(entry => {
    const instrumentName = entry.instrument || entry.Instrument || "";
    return instrumentName.includes("- Called") || instrumentName.includes("- Commitment");
  });

  if (hasCallCommitmentFormat) {
    // Use the original pairing logic for data with "- Called" / "- Commitment" format
    const instrumentMap = new Map<string, { called?: RawDataEntry; commitment?: RawDataEntry }>();

    rawData.forEach((entry) => {
      const instrumentField = entry.instrument || entry.Instrument;
      if (!instrumentField || typeof instrumentField !== 'string' || !instrumentField.trim()) {
        console.warn("Skipping invalid entry:", entry);
        return;
      }

      let instrumentName = instrumentField.trim();
      let status: "Called" | "Commitment" | null = null;

      if (instrumentName.includes("- Called")) {
        instrumentName = instrumentName.replace("- Called", "").trim();
        status = "Called";
      } else if (instrumentName.includes("- Commitment")) {
        instrumentName = instrumentName.replace("- Commitment", "").trim();
        status = "Commitment";
      }

      if (!status || !instrumentName) return;

      if (!instrumentMap.has(instrumentName)) {
        instrumentMap.set(instrumentName, {});
      }

      const instrumentEntry = instrumentMap.get(instrumentName)!;
      if (status === "Called") {
        instrumentEntry.called = entry;
      } else {
        instrumentEntry.commitment = entry;
      }
    });

    const pairedInstruments: ProcessedInstrument[] = [];
    const unpairedInstruments: ProcessedInstrument[] = [];

    instrumentMap.forEach((data, instrumentName) => {
      const hasCall = !!data.called;
      const hasCommitment = !!data.commitment;
      const assetClass = (data.commitment?.assetClass || data.commitment?.["Asset Class"] || data.called?.assetClass || data.called?.["Asset Class"] || "Unknown").toString();

      const processedInstrument: ProcessedInstrument = {
        id: id++,
        instrument: instrumentName,
        assetClass,
        calledAmount: data.called?.outstanding || 0,
        commitmentAmount: data.commitment?.outstanding || 0,
        status: hasCall && hasCommitment ? "Paired" : "Unpaired",
        missingType: !hasCall ? "Called" : !hasCommitment ? "Commitment" : undefined,
      };

      if (hasCall && hasCommitment) {
        pairedInstruments.push(processedInstrument);
      } else {
        unpairedInstruments.push(processedInstrument);
      }
    });

    return { pairedInstruments, unpairedInstruments };
  } else {
    // Handle your actual data format (without "- Called" / "- Commitment" suffixes)
    rawData.forEach((entry) => {
      const instrumentName = (entry.instrument || entry.Instrument || "").trim();
      if (!instrumentName) {
        console.warn("Skipping entry without instrument name:", entry);
        return;
      }

      const assetClass = (entry.assetClass || entry["Asset Class"] || "Unknown").toString();

      // Get value - try multiple possible fields
      let value = 0;
      if (entry.outstanding) {
        value = typeof entry.outstanding === 'number' ? entry.outstanding : parseFloat(entry.outstanding.toString()) || 0;
      } else if (entry["Value WA Portfolio CCY"]) {
        value = typeof entry["Value WA Portfolio CCY"] === 'number' ? entry["Value WA Portfolio CCY"] : parseFloat(entry["Value WA Portfolio CCY"].toString()) || 0;
      } else if (entry["Value Portfolio CCY"]) {
        value = typeof entry["Value Portfolio CCY"] === 'number' ? entry["Value Portfolio CCY"] : parseFloat(entry["Value Portfolio CCY"].toString()) || 0;
      }

      const processedInstrument: ProcessedInstrument = {
        id: id++,
        instrument: instrumentName,
        assetClass,
        calledAmount: 0, // Your data doesn't seem to have called amounts
        commitmentAmount: value,
        status: "Paired", // Treat all as paired since we don't have pairing logic
        missingType: undefined,
      };

      processedInstruments.push(processedInstrument);
    });

    return {
      pairedInstruments: processedInstruments,
      unpairedInstruments: [],
    };
  }
}

export function DashboardWithUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [currentData, setCurrentData] = useState(defaultData);
  const [processedData, setProcessedData] = useState<{
    pairedInstruments: ProcessedInstrument[];
    unpairedInstruments: ProcessedInstrument[];
  } | null>(null);

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(files);

    // If files are uploaded, try to use the first one for processing
    if (files.length > 0) {
      const firstFile = files[0];
      let rawData: RawDataEntry[] = [];

      // Extract data from different possible structures
      if (firstFile.content && Array.isArray(firstFile.content)) {
        rawData = firstFile.content;
      } else if (firstFile.content && firstFile.content.data && Array.isArray(firstFile.content.data)) {
        rawData = firstFile.content.data;
      }

      if (rawData.length > 0) {
        try {
          console.log("Processing raw data:", rawData.slice(0, 3)); // Log first 3 entries for debugging

          // Validate data structure - support both formats
          const validEntries = rawData.filter(entry => {
            if (!entry || typeof entry !== 'object') return false;

            // Check for instrument field (either lowercase or uppercase)
            const instrumentField = entry.instrument || entry.Instrument;
            return typeof instrumentField === 'string' && instrumentField.trim().length > 0;
          });

          if (validEntries.length === 0) {
            console.error("No valid entries found in uploaded data");
            console.error("Sample of invalid entries:", rawData.slice(0, 5));
            console.error("Expected format: Each entry should have an 'instrument' field as a non-empty string");

            // Check what fields are actually present
            if (rawData.length > 0) {
              const sampleEntry = rawData[0];
              console.error("First entry keys:", Object.keys(sampleEntry || {}));
              console.error("First entry:", sampleEntry);
            }
            return;
          }

          console.log(`Found ${validEntries.length} valid entries out of ${rawData.length} total entries`);

          // Process the data to find paired/unpaired instruments
          const processed = processInstrumentData(validEntries);
          setProcessedData(processed);

          console.log("Processed data:", {
            paired: processed.pairedInstruments.length,
            unpaired: processed.unpairedInstruments.length
          });

          // Convert paired instruments to the format expected by the table
          const tableData = processed.pairedInstruments.map(instrument => ({
            id: instrument.id,
            instrument: instrument.instrument,
            assetClass: instrument.assetClass,
            status: "Commitment", // Show as commitment for the main table
            outstanding: instrument.commitmentAmount,
          }));

          setCurrentData(tableData);
        } catch (error) {
          console.error("Error processing uploaded file:", error);
        }
      }
    }
  };

  const resetToDefaultData = () => {
    setCurrentData(defaultData);
    setUploadedFiles([]);
    setProcessedData(null);
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          
          {/* File Upload Section */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconDatabase className="h-5 w-5" />
                    <CardTitle>Data Management</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    {uploadedFiles.length > 0 && (
                      <Badge variant="secondary">
                        {uploadedFiles.length} file(s) loaded
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowFileUpload(!showFileUpload)}
                    >
                      <IconUpload className="h-4 w-4 mr-2" />
                      {showFileUpload ? 'Hide Upload' : 'Upload Data'}
                    </Button>
                    {uploadedFiles.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetToDefaultData}
                      >
                        Reset to Default
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              {showFileUpload && (
                <CardContent>
                  <FileUpload
                    onFileUpload={handleFileUpload}
                    maxFiles={3}
                  />
                  <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Supported JSON Formats:</h4>
                    <div className="space-y-4">
                      <div>
                        <p className="text-xs font-medium mb-1">Format 1: Paired Instruments (with Called/Commitment)</p>
                        <pre className="text-xs text-muted-foreground overflow-x-auto">
{`[
  {
    "instrument": "Fund Name - Called",
    "assetClass": "Private Markets",
    "outstanding": 1900000.00
  },
  {
    "instrument": "Fund Name - Commitment",
    "assetClass": "Private Markets",
    "outstanding": 1000000.00
  }
]`}</pre>
                      </div>
                      <div>
                        <p className="text-xs font-medium mb-1">Format 2: Portfolio Data (like your testYad.json)</p>
                        <pre className="text-xs text-muted-foreground overflow-x-auto">
{`[
  {
    "Instrument": "CASH ACCOUNT IN USD",
    "Asset Class": "Cash",
    "Value WA Portfolio CCY": "3641.19",
    "Portfolio": "Portfolio Name",
    ...
  }
]`}</pre>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-muted-foreground">
                      <p><strong>The system will automatically detect which format you're using.</strong></p>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Upload JSON files with investment fund data to visualize portfolio metrics,
                      performance analysis, and risk management data.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
          </div>

          {/* Chart Section */}
          <div className="px-4 lg:px-6">
            <ChartAreaInteractive />
          </div>

          {/* Data Table Section */}
          <DataTable data={currentData} />

          {/* Anomalies Section - Only show if we have processed data with unpaired instruments */}
          {processedData && processedData.unpairedInstruments.length > 0 && (
            <div className="px-4 lg:px-6 mt-8">
              <Card>
                <CardHeader>
                  <CardTitle className="text-red-600 dark:text-red-400">
                    ⚠️ Anomalies: Unpaired Positions
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    These instruments have only one entry (either Called or Commitment) and need attention.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {processedData.unpairedInstruments.map((instrument) => (
                      <div
                        key={instrument.id}
                        className="flex items-center justify-between p-4 border rounded-lg bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{instrument.instrument}</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Asset Class: {instrument.assetClass}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="destructive" className="mb-2">
                            Missing: {instrument.missingType}
                          </Badge>
                          <p className="text-sm font-medium">
                            {instrument.calledAmount > 0 && (
                              <span>Called: ${instrument.calledAmount.toLocaleString()}</span>
                            )}
                            {instrument.commitmentAmount > 0 && (
                              <span>Commitment: ${instrument.commitmentAmount.toLocaleString()}</span>
                            )}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
