"use client";

import React, { useState } from "react";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { DataTable } from "@/components/data-table";
import { FileUpload } from "@/components/file-upload";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IconUpload, IconDatabase } from "@tabler/icons-react";
import { Badge } from "@/components/ui/badge";

import defaultData from "../app/dashboard/finance-data.json";

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  content: any;
  uploadedAt: Date;
}

interface ProcessedInstrument {
  id: number;
  instrument: string;
  assetClass: string;
  calledAmount: number;
  commitmentAmount: number;
  status: "Paired" | "Unpaired";
  missingType?: "Called" | "Commitment";
}

interface RawDataEntry {
  instrument: string;
  assetClass?: string;
  outstanding?: number;
  [key: string]: any;
}

// Function to process uploaded data and find paired/unpaired instruments
function processInstrumentData(rawData: RawDataEntry[]): {
  pairedInstruments: ProcessedInstrument[];
  unpairedInstruments: ProcessedInstrument[];
} {
  const instrumentMap = new Map<string, { called?: RawDataEntry; commitment?: RawDataEntry }>();

  // Group entries by instrument name (removing status suffixes)
  rawData.forEach((entry) => {
    // Validate entry has required fields
    if (!entry || typeof entry.instrument !== 'string' || !entry.instrument.trim()) {
      console.warn("Skipping invalid entry:", entry);
      return;
    }

    let instrumentName = entry.instrument.trim();
    let status: "Called" | "Commitment" | null = null;

    // Extract status from instrument name
    if (instrumentName.includes("- Called")) {
      instrumentName = instrumentName.replace("- Called", "").trim();
      status = "Called";
    } else if (instrumentName.includes("- Commitment")) {
      instrumentName = instrumentName.replace("- Commitment", "").trim();
      status = "Commitment";
    }

    if (!status || !instrumentName) return; // Skip entries without clear status or empty names

    // Initialize instrument entry if not exists
    if (!instrumentMap.has(instrumentName)) {
      instrumentMap.set(instrumentName, {});
    }

    const instrumentEntry = instrumentMap.get(instrumentName)!;
    if (status === "Called") {
      instrumentEntry.called = entry;
    } else {
      instrumentEntry.commitment = entry;
    }
  });

  const pairedInstruments: ProcessedInstrument[] = [];
  const unpairedInstruments: ProcessedInstrument[] = [];
  let id = 1;

  // Process each instrument to determine if paired or unpaired
  instrumentMap.forEach((data, instrumentName) => {
    const hasCall = !!data.called;
    const hasCommitment = !!data.commitment;

    // Determine asset class (prefer from commitment, fallback to called)
    const assetClass = (data.commitment?.assetClass || data.called?.assetClass || "Unknown").toString();

    const processedInstrument: ProcessedInstrument = {
      id: id++,
      instrument: instrumentName,
      assetClass,
      calledAmount: data.called?.outstanding || 0,
      commitmentAmount: data.commitment?.outstanding || 0,
      status: hasCall && hasCommitment ? "Paired" : "Unpaired",
      missingType: !hasCall ? "Called" : !hasCommitment ? "Commitment" : undefined,
    };

    if (hasCall && hasCommitment) {
      pairedInstruments.push(processedInstrument);
    } else {
      unpairedInstruments.push(processedInstrument);
    }
  });

  return { pairedInstruments, unpairedInstruments };
}

export function DashboardWithUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [currentData, setCurrentData] = useState(defaultData);
  const [processedData, setProcessedData] = useState<{
    pairedInstruments: ProcessedInstrument[];
    unpairedInstruments: ProcessedInstrument[];
  } | null>(null);

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(files);

    // If files are uploaded, try to use the first one for processing
    if (files.length > 0) {
      const firstFile = files[0];
      let rawData: RawDataEntry[] = [];

      // Extract data from different possible structures
      if (firstFile.content && Array.isArray(firstFile.content)) {
        rawData = firstFile.content;
      } else if (firstFile.content && firstFile.content.data && Array.isArray(firstFile.content.data)) {
        rawData = firstFile.content.data;
      }

      if (rawData.length > 0) {
        try {
          console.log("Processing raw data:", rawData.slice(0, 3)); // Log first 3 entries for debugging

          // Validate data structure
          const validEntries = rawData.filter(entry =>
            entry &&
            typeof entry === 'object' &&
            typeof entry.instrument === 'string' &&
            entry.instrument.trim().length > 0
          );

          if (validEntries.length === 0) {
            console.error("No valid entries found in uploaded data");
            console.error("Sample of invalid entries:", rawData.slice(0, 5));
            console.error("Expected format: Each entry should have an 'instrument' field as a non-empty string");

            // Check what fields are actually present
            if (rawData.length > 0) {
              const sampleEntry = rawData[0];
              console.error("First entry keys:", Object.keys(sampleEntry || {}));
              console.error("First entry:", sampleEntry);
            }
            return;
          }

          console.log(`Found ${validEntries.length} valid entries out of ${rawData.length} total entries`);

          // Process the data to find paired/unpaired instruments
          const processed = processInstrumentData(validEntries);
          setProcessedData(processed);

          console.log("Processed data:", {
            paired: processed.pairedInstruments.length,
            unpaired: processed.unpairedInstruments.length
          });

          // Convert paired instruments to the format expected by the table
          const tableData = processed.pairedInstruments.map(instrument => ({
            id: instrument.id,
            instrument: instrument.instrument,
            assetClass: instrument.assetClass,
            status: "Commitment", // Show as commitment for the main table
            outstanding: instrument.commitmentAmount,
          }));

          setCurrentData(tableData);
        } catch (error) {
          console.error("Error processing uploaded file:", error);
        }
      }
    }
  };

  const resetToDefaultData = () => {
    setCurrentData(defaultData);
    setUploadedFiles([]);
    setProcessedData(null);
  };

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          
          {/* File Upload Section */}
          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconDatabase className="h-5 w-5" />
                    <CardTitle>Data Management</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    {uploadedFiles.length > 0 && (
                      <Badge variant="secondary">
                        {uploadedFiles.length} file(s) loaded
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowFileUpload(!showFileUpload)}
                    >
                      <IconUpload className="h-4 w-4 mr-2" />
                      {showFileUpload ? 'Hide Upload' : 'Upload Data'}
                    </Button>
                    {uploadedFiles.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetToDefaultData}
                      >
                        Reset to Default
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              {showFileUpload && (
                <CardContent>
                  <FileUpload
                    onFileUpload={handleFileUpload}
                    maxFiles={3}
                  />
                  <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">Expected JSON Format:</h4>
                    <pre className="text-xs text-muted-foreground overflow-x-auto">
{`[
  {
    "instrument": "Blackstone Capital Partners VIII - Called",
    "assetClass": "Private Markets",
    "outstanding": 1900000.00
  },
  {
    "instrument": "Blackstone Capital Partners VIII - Commitment",
    "assetClass": "Private Markets",
    "outstanding": 1000000.00
  },
  ...
]`}</pre>
                    <div className="mt-2 text-xs text-muted-foreground">
                      <p><strong>Key Requirements:</strong></p>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Instrument names must end with "- Called" or "- Commitment"</li>
                        <li>Only instruments with BOTH Called and Commitment entries will appear in main table</li>
                        <li>Instruments with only one entry will appear in "Anomalies" section</li>
                        <li>Data will be filtered by Asset Class (Real Estate vs Others)</li>
                      </ul>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Upload JSON files with investment fund data to visualize portfolio metrics,
                      performance analysis, and risk management data.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
          </div>

          {/* Chart Section */}
          <div className="px-4 lg:px-6">
            <ChartAreaInteractive />
          </div>

          {/* Data Table Section */}
          <DataTable data={currentData} />

          {/* Anomalies Section - Only show if we have processed data with unpaired instruments */}
          {processedData && processedData.unpairedInstruments.length > 0 && (
            <div className="px-4 lg:px-6 mt-8">
              <Card>
                <CardHeader>
                  <CardTitle className="text-red-600 dark:text-red-400">
                    ⚠️ Anomalies: Unpaired Positions
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    These instruments have only one entry (either Called or Commitment) and need attention.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {processedData.unpairedInstruments.map((instrument) => (
                      <div
                        key={instrument.id}
                        className="flex items-center justify-between p-4 border rounded-lg bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{instrument.instrument}</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Asset Class: {instrument.assetClass}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="destructive" className="mb-2">
                            Missing: {instrument.missingType}
                          </Badge>
                          <p className="text-sm font-medium">
                            {instrument.calledAmount > 0 && (
                              <span>Called: ${instrument.calledAmount.toLocaleString()}</span>
                            )}
                            {instrument.commitmentAmount > 0 && (
                              <span>Commitment: ${instrument.commitmentAmount.toLocaleString()}</span>
                            )}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
