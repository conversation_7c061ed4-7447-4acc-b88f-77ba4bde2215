<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Other - Commitment Analysis</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root { --bg-dark: #121828; --card-dark: #1b253b; --border-dark: #3a4763; --header-dark: #2a3550; --text-light: #e0e6f0; --text-muted: #8899b8; --accent-blue: #3b82f6; --positive-green: #2ecc71; --warning-yellow: #f6ad55; --font-main: 'Open Sans', sans-serif; --font-heading: 'Roboto', sans-serif; }
        body { font-family: var(--font-main); background-color: var(--bg-dark); color: var(--text-light); margin: 0; padding: 20px; font-size: 16px; line-height: 1.6; }
        .container { max-width: 1600px; margin: 20px auto; padding: 0 20px; }
        header { text-align: center; margin-bottom: 40px; }
        header h1 { font-family: var(--font-heading); color: #ffffff; font-size: 2.2rem; font-weight: 700; margin-bottom: 5px; }
        header .subtitle { color: var(--text-muted); font-size: 1.1rem; }
        .print-button { display: block; width: 200px; margin: 0 auto 40px auto; padding: 12px 20px; background-color: var(--accent-blue); color: white; border: none; border-radius: 6px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background-color 0.3s, transform 0.1s; }
        .print-button:hover { background-color: #2563eb; }
        .print-button:active { transform: scale(0.98); }
        .total-card { background-color: var(--card-dark); border-left: 4px solid var(--accent-blue); padding: 25px; margin: 0 auto 40px auto; border-radius: 8px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); text-align: center; max-width: 500px; }
        .total-card .total-label { font-size: 1.1rem; color: var(--text-muted); margin-bottom: 8px; }
        .total-card .total-value { font-family: var(--font-heading); font-size: 2.8rem; color: #ffffff; font-weight: 700; }
        h2.section-title { font-family: var(--font-heading); color: var(--text-light); font-size: 1.6rem; margin-top: 50px; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid var(--border-dark); }
        .table-container { width: 100%; overflow-x: auto; background-color: var(--card-dark); border-radius: 8px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); margin-bottom: 40px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 14px 18px; text-align: left; border-bottom: 1px solid var(--border-dark); }
        thead th { background-color: var(--header-dark); color: #ffffff; font-family: var(--font-heading); font-size: 0.85em; text-transform: uppercase; letter-spacing: 0.05em; position: sticky; top: 0; }
        tbody tr:hover { background-color: #3e4c63; }
        .year-header td { background-color: var(--header-dark); color: var(--accent-blue); font-family: var(--font-heading); font-weight: 700; font-size: 1.1em; letter-spacing: 0.5px; }
        .year-header .year-total-label { text-align: right; font-size: 0.9em; color: var(--text-muted); font-weight: 500; }
        .year-header .year-total { font-size: 0.9em; font-weight: 500; color: var(--text-muted); }
        .subtotal-row td { background-color: #232e44; font-weight: 700; }
        .subtotal-row .subtotal-label { text-align: right; color: var(--text-muted); }
        .numeric { text-align: right; font-family: 'Roboto', monospace; white-space: nowrap; }
        .outstanding { font-weight: 700; color: var(--positive-green); }
        @media print {
            @page { size: A4 portrait; margin: 1.5cm; }
            :root { --positive-green: #000000; --warning-yellow: #000000; }
            body { background-color: #ffffff; color: #000000; font-size: 9.5pt; padding: 0; }
            header, .print-button, footer { display: none; }
            .container { padding: 0; margin: 0; max-width: 100%; }
            .total-card { background-color: #f2f2f2; border: 1px solid #ccc; color: #000; box-shadow: none; margin-bottom: 25px; page-break-after: avoid; }
            .total-card .total-label { color: #555; }
            .total-card .total-value { color: #000; font-size: 2rem; }
            h2.section-title { font-size: 14pt; color: #000; border-bottom: 2px solid #000; margin-top: 20px; page-break-before: auto; page-break-after: avoid; }
            .table-container { box-shadow: none; border: 1px solid #ccc; border-radius: 0; background-color: transparent; page-break-inside: auto; margin-bottom: 20px; }
            table { page-break-inside:auto; }
            tr { page-break-inside:avoid; page-break-after:auto; }
            thead th { background-color: #e8e8e8 !important; color: #000 !important; border-bottom: 2px solid #000; }
            td, th { padding: 6px 8px; border: 1px solid #ddd !important; color: #000 !important; }
            tbody tr:hover { background-color: transparent !important; }
            .year-header td, .subtotal-row td { background-color: #f2f2f2 !important; font-weight: bold; }
            .year-header td { color: #000 !important; }
            .chart-container { page-break-before: always; box-shadow: none; border: 1px solid #ccc; width: 100% !important; height: 70vh !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header><h1>All Other Commitments Analysis</h1><p class="subtitle">Detailed Breakdown of Private Markets, Venture Capital, and Other Commitments</p></header>
        <button class="print-button" onclick="window.print()">Print / Save as PDF</button>
        <div class="total-card">
            <div class="total-label">Total Outstanding Commitment (Non-Real Estate)</div>
            <div class="total-value" id="grand-total">3644435.50</div>
        </div>
        <h2 class="section-title">Paired Commitments</h2>
        <div class="table-container">
            <table>
                <thead><tr><th>Instrument</th><th>Asset Class</th><th class="numeric">Commitment (USD)</th><th class="numeric">Called (USD)</th><th class="numeric outstanding">Outstanding (USD)</th></tr></thead>
                <tbody>
                    <tr class="year-header"><td colspan="3">2005 Commitments</td><td class="year-total-label">Total Commitment:</td><td class="numeric currency year-total">1000000.00</td></tr>
                    <tr><td>ISF III 2005</td><td>Venture Capital</td><td class="numeric currency">1000000.00</td><td class="numeric currency">381454.17</td><td class="numeric currency outstanding">618545.83</td></tr>
                    <tr class="subtotal-row"><td colspan="4" class="subtotal-label">2005 Outstanding Subtotal:</td><td class="numeric currency outstanding">618545.83</td></tr>
                    <tr class="year-header"><td colspan="3">2020 Commitments</td><td class="year-total-label">Total Commitment:</td><td class="numeric currency year-total">1900000.00</td></tr>
                    <tr><td>TARGET ISRAELI BUYOUT 2020 LP CLASS A</td><td>Private Markets</td><td class="numeric currency">900000.00</td><td class="numeric currency">441000.00</td><td class="numeric currency outstanding">459000.00</td></tr>
                    <tr><td>SLA II / Silver Lake II</td><td>Private Markets</td><td class="numeric currency">1000000.00</td><td class="numeric currency">745479.38</td><td class="numeric currency outstanding">254520.62</td></tr>
                    <tr class="subtotal-row"><td colspan="4" class="subtotal-label">2020 Outstanding Subtotal:</td><td class="numeric currency outstanding">713520.62</td></tr>
                    <tr class="year-header"><td colspan="3">2021 Commitments</td><td class="year-total-label">Total Commitment:</td><td class="numeric currency year-total">4674334.69</td></tr>
                    <tr><td>FOCUS TECH 2021 LP CLASS A SHARES</td><td>Private Markets</td><td class="numeric currency">1000000.00</td><td class="numeric currency">950000.00</td><td class="numeric currency outstanding">50000.00</td></tr>
                    <tr><td>Frux Debt Fund II 2021</td><td>Private Markets</td><td class="numeric currency">1174334.69</td><td class="numeric currency">1120583.84</td><td class="numeric currency outstanding">53750.85</td></tr>
                    <tr><td>HarbourVest Co-Investment VI 2021</td><td>Private Markets</td><td class="numeric currency">1000000.00</td><td class="numeric currency">839976.73</td><td class="numeric currency outstanding">160023.27</td></tr>
                    <tr><td>Harbourvest Global Access 2021</td><td>Private Markets</td><td class="numeric currency">1000000.00</td><td class="numeric currency">674019.58</td><td class="numeric currency outstanding">325980.42</td></tr>
                    <tr><td>Tiger Global XV Private Investors (bankable) 2021</td><td>Private Markets</td><td class="numeric currency">500000.00</td><td class="numeric currency">475025.73</td><td class="numeric currency outstanding">24974.27</td></tr>
                    <tr><td>GA 2021 PRIVATE INVESTORS CLASS A 2021</td><td>Venture Capital</td><td class="numeric currency">1000000.00</td><td class="numeric currency">935598.18</td><td class="numeric currency outstanding">64401.82</td></tr>
                    <tr class="subtotal-row"><td colspan="4" class="subtotal-label">2021 Outstanding Subtotal:</td><td class="numeric currency outstanding">679130.63</td></tr>
                    <tr class="year-header"><td colspan="3">2022 Commitments</td><td class="year-total-label">Total Commitment:</td><td class="numeric currency year-total">500000.00</td></tr>
                    <tr><td>Lexington X Private Investors 2022</td><td>Venture Capital</td><td class="numeric currency">500000.00</td><td class="numeric currency">244614.95</td><td class="numeric currency outstanding">255385.05</td></tr>
                    <tr class="subtotal-row"><td colspan="4" class="subtotal-label">2022 Outstanding Subtotal:</td><td class="numeric currency outstanding">255385.05</td></tr>
                    <tr class="year-header"><td colspan="3">2024 Commitments</td><td class="year-total-label">Total Commitment:</td><td class="numeric currency year-total">2587167.33</td></tr>
                    <tr><td>Frux Fund III SCSp RAIF 2024</td><td>Private Markets</td><td class="numeric currency">587167.33</td><td class="numeric currency">472669.71</td><td class="numeric currency outstanding">114497.62</td></tr>
                    <tr><td>LTO 4 2024</td><td>Private Markets</td><td class="numeric currency">2000000.00</td><td class="numeric currency">831144.25</td><td class="numeric currency outstanding">1168855.75</td></tr>
                    <tr class="subtotal-row"><td colspan="4" class="subtotal-label">2024 Outstanding Subtotal:</td><td class="numeric currency outstanding">1283353.37</td></tr>
                    <tr class="year-header"><td colspan="3">Unspecified Year</td><td class="year-total-label">Total Commitment:</td><td class="numeric currency year-total">350000.00</td></tr>
                    <tr><td>CREDIT SUISSE PRIVATE EQUITY PLATFORM</td><td>Private Markets</td><td class="numeric currency">350000.00</td><td class="numeric currency">255500.00</td><td class="numeric currency outstanding">94500.00</td></tr>
                    <tr class="subtotal-row"><td colspan="4" class="subtotal-label">Unspecified Year Outstanding Subtotal:</td><td class="numeric currency outstanding">94500.00</td></tr>
                </tbody>
            </table>
        </div>
        <h2 class="section-title">Total Commitments by Year (Non-Real Estate)</h2>
        <div class="chart-container"><canvas id="commitmentsChart"></canvas></div>
    </div>
    <footer><p>Data as of June 30, 2025. All values are in USD. Report generated on demand.</p></footer>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const formatCurrency = (value) => { const number = parseFloat(value); if (isNaN(number)) return value; return number.toLocaleString('en-US', { style: 'currency', currency: 'USD' }); };
            const formatCurrencyShort = (value) => { const number = parseFloat(value); if (isNaN(number)) return value; if (number >= 1000000) { return '$' + (number / 1000000).toFixed(2) + 'M'; } if (number >= 1000) { return '$' + (number / 1000).toFixed(1) + 'K'; } return '$' + number; };
            document.querySelectorAll('.currency').forEach(cell => { cell.textContent = formatCurrency(cell.textContent); });
            const grandTotalEl = document.getElementById('grand-total');
            if(grandTotalEl) { grandTotalEl.textContent = formatCurrency(grandTotalEl.textContent); }
            const ctx = document.getElementById('commitmentsChart').getContext('2d');
            const chartData = {
                labels: ['2005', '2020', '2021', '2022', '2024', 'Unspecified'],
                datasets: [{
                    label: 'Total Commitment (USD)',
                    data: [1000000.00, 1900000.00, 5674334.69, 500000.00, 2587167.33, 350000.00],
                    backgroundColor: 'rgba(59, 130, 246, 0.6)', borderColor: 'rgba(59, 130, 246, 1)', borderWidth: 1, borderRadius: 4
                }]
            };
            new Chart(ctx, { type: 'bar', data: chartData, options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false }, tooltip: { backgroundColor: '#1b253b', titleFont: { size: 14, family: 'Roboto' }, bodyFont: { size: 12, family: 'Lato' }, callbacks: { label: function(context) { return 'Commitment: ' + formatCurrency(context.parsed.y); } } } }, scales: { y: { beginAtZero: true, grid: { color: 'rgba(74, 85, 104, 0.5)' }, ticks: { color: '#a0aec0', callback: function(value) { return formatCurrencyShort(value); } } }, x: { grid: { display: false }, ticks: { color: '#a0aec0', font: { size: 12 } } } } } });
        });
    </script>
</body>
</html>